import { requestClient } from '#/api/request';

export namespace HotspotApi {
  /** 热点数据项接口 */
  export interface HotspotItem {
    hotindex: string; // 热度值
    label: number; // 标签
    word: string; // 热点名称
  }

  /** 获取热点列表响应接口 */
  export interface GetHotspotResponse {
    data: HotspotItem[];
  }

  /** 获取初始文案请求参数接口 */
  export interface GetInitialContentParams {
    content: string;
    domainName: string;
    ipStatus: number;
    requestIp: string;
  }

  /** 获取初始文案响应接口 */
  export interface GetInitialContentResponse {
    data: string;
  }

  /** 一键仿写请求参数接口 */
  export interface HotCopyParams {
    content: string;
  }
}

/**
 * 获取热点列表
 * @returns Promise<HotspotApi.HotspotItem[]>
 */
export function getHotspotList(): Promise<HotspotApi.HotspotItem[]> {
  return requestClient.post('/mobile/aiCreate/getHotspot');
}

/**
 * 获取初始文案内容
 * @param params 请求参数
 * @returns Promise<string>
 */
export async function getInitialContent(
  params: HotspotApi.GetInitialContentParams,
): Promise<string> {
  // 使用fetch直接调用外部API
  const response = await fetch('https://vapi.weijuyunke.com/api/doBao/copyImitation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const result = await response.json();
  return result.data || '';
}

/**
 * 获取一键仿写流式接口URL
 * 注意：此接口返回流式数据，需要使用流式请求工具处理
 */
export function getHotCopyUrl(): string {
  return '/mobile/ai_dialogue/hotCopy';
}
