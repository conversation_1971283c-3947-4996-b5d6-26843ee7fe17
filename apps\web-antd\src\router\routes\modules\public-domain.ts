import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:megaphone',
      order: 2,
      title: $t('page.menu.publicDomain.title'),
    },
    name: 'PublicDomain',
    path: '/public-domain',
    redirect: '/public-domain/ai-digital-human',
    children: [
      {
        meta: {
          icon: 'lucide:user-circle',
          title: $t('page.menu.publicDomain.aiDigitalHuman'),
        },
        name: 'AIDigitalHuman',
        path: '/public-domain/ai-digital-human',
        component: () => import('#/views/ai-digital-human/index.vue'),
      },

      {
        meta: {
          icon: 'lucide:user-circle',
          title: '内容选题',
          query: {
            title: '内容选题',
            type: '1',
          },
        },
        name: '内容选题',
        path: '/public-domain/content-topic-selection',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || '内容选题',
                type: to.query.type || '1',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          hideInMenu: true, // 不在菜单中显示
          hideInBreadcrumb: true,
          hideInTab: true,
          title: '声音克隆',
        },
        name: 'VoiceClone',
        path: '/public-domain/voice-clone',
        component: () => import('#/views/ai-digital-human/ai-voice-clone.vue'),
      },
      {
        meta: {
          hideInMenu: true, // 不在菜单中显示
          hideInBreadcrumb: true,
          hideInTab: true,
          title: '视频创作',
        },
        name: 'VideoCreation',
        path: '/public-domain/video-creation',
        component: () =>
          import('#/views/ai-digital-human/ai-video-creation.vue'),
      },
      {
        meta: {
          icon: 'lucide:pen-tool',
          title: $t('page.menu.publicDomain.aiCopywriting'),
          query: {
            title: '文案编导',
            type: '2',
          },
        },
        name: 'AICopywriting',
        path: '/public-domain/ai-copywriting',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || '文案编导',
                type: to.query.type || '2',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:video',
          title: $t('page.menu.publicDomain.aiMixedVideo'),
        },
        name: 'AIMixedVideo',
        path: '/public-domain/ai-mixed-video',
        component: () => import('#/views/ai-video-mixing/index.vue'),
      },
      {
        meta: {
          icon: 'lucide:trending-up',
          title: $t('page.menu.publicDomain.aiViralImitation'),
        },
        name: 'AIViralImitation',
        path: '/public-domain/ai-viral-imitation',
        component: () => import('#/views/common/under-development.vue'),
      },
      {
        meta: {
          icon: 'lucide:flame',
          title: $t('page.menu.publicDomain.aiTrendFollowing'),
        },
        name: 'AITrendFollowing',
        path: '/public-domain/ai-trend-following',
        component: () => import('#/views/ai-hotspot-tracking/index.vue'),
      },
      {
        meta: {
          hideInMenu: true, // 不在菜单中显示
          hideInBreadcrumb: true,
          hideInTab: true,
          icon: 'lucide:grid-3x3',
          title: $t('page.menu.publicDomain.aiMatrixManagement'),
        },
        name: 'AIMatrixManagement',
        path: '/public-domain/ai-matrix-management',
        // component: () => import('#/views/public-domain/matrix-management/index.vue'),
        redirect: '/public-domain/matrix-management-dashboard',
        children: [
          {
            meta: {
              hideInMenu: true, // 不在菜单中显示
              hideInBreadcrumb: true,
              hideInTab: true,
              icon: 'lucide:users',
              title: $t('matrix-management.homepage.title'),
            },
            name: 'AIMatrixManagementHomepage',
            path: '/public-domain/ai-matrix-management/homepage',
            component: () =>
              import('#/views/public-domain/matrix-management/matrix.vue'),
          },
          {
            meta: {
              hideInMenu: true, // 不在菜单中显示
              hideInBreadcrumb: true,
              hideInTab: true,
              icon: 'lucide:users',
              title: $t('matrix-management.account-management.title'),
            },
            name: 'AIMatrixManagementAccountManagement',
            path: '/public-domain/ai-matrix-management/account-management',
            component: () =>
              import('#/views/public-domain/matrix-management/account.vue'),
          },
          {
            meta: {
              hideInMenu: true, // 不在菜单中显示
              hideInBreadcrumb: true,
              hideInTab: true,
              icon: 'lucide:users',
              title: $t('matrix-management.group-management.title'),
            },
            name: 'AIMatrixManagementGroupManagement',
            path: '/public-domain/ai-matrix-management/group-management',
            component: () =>
              import(
                '#/views/public-domain/matrix-management/group-management.vue'
              ),
          },
          {
            meta: {
              hideInMenu: true, // 不在菜单中显示
              hideInBreadcrumb: true,
              hideInTab: true,
              icon: 'lucide:users',
              title: $t('matrix-management.publish-management.title'),
            },
            name: 'AIMatrixManagementPublishManagement',
            path: '/public-domain/ai-matrix-management/publish-management',
            component: () =>
              import(
                '#/views/public-domain/matrix-management/publish-management.vue'
              ),
          },
        ],
      },
      // 新的矩阵管理主页面
      {
        meta: {
          icon: 'lucide:layout-dashboard',
          title: $t('matrix-management.dashboard.title'),
        },
        name: 'MatrixManagementDashboard',
        path: '/public-domain/matrix-management-dashboard',
        component: () =>
          import('#/views/public-domain/matrix-management/dashboard.vue'),
      },
      {
        meta: {
          icon: 'lucide:book-open',
          title: $t('page.menu.publicDomain.aiXiaohongshuNotes'),
          query: {
            title: '小红书笔记',
            type: '15',
          },
        },
        name: 'AIXiaohongshuNotes',
        path: '/public-domain/ai-xiaohongshu-notes',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        beforeEnter: (to, _from, next) => {
          // 确保查询参数存在
          if (!to.query.title || !to.query.type) {
            next({
              path: to.path,
              query: {
                ...to.query,
                title: to.query.title || '小红书笔记',
                type: to.query.type || '15',
              },
            });
          } else {
            next();
          }
        },
      },
      {
        meta: {
          icon: 'lucide:radio',
          title: $t('page.menu.publicDomain.aiLiveStreaming'),
        },
        name: 'AILiveStreaming',
        path: '/public-domain/ai-live-streaming',
        component: () => import('#/views/common/under-development.vue'),
      },
      {
        meta: {
          icon: 'lucide:brain-circuit',
          title: $t('page.menu.publicDomain.aiSmartInvestment'),
        },
        name: 'AISmartInvestment',
        path: '/public-domain/ai-smart-investment',
        component: () => import('#/views/common/under-development.vue'),
      },
    ],
  },
];

export default routes;
