<script lang="ts" setup>
import type { HotspotApi } from '#/api/core/hotspot';

import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Button,
  Card,
  Empty,
  Input,
  List,
  message,
  Spin,
} from 'ant-design-vue';

import {
  getHotspotList,
  getInitialContent,
  getHotCopyUrl,
} from '#/api/core/hotspot';
import { createStreamHandler } from '#/utils/stream-request';

defineOptions({ name: 'AIHotspotTracking' });

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const hotspotList = ref<HotspotApi.HotspotItem[]>([]);
const selectedHotspot = ref<HotspotApi.HotspotItem | null>(null);
const textContent = ref('');
const isRewriting = ref(false);
const contentLoading = ref(false);

// 计算属性
const canGoToCreation = computed(() => {
  return textContent.value.trim().length > 0 && !isRewriting.value;
});

// 加载热点列表
const loadHotspotList = async () => {
  try {
    loading.value = true;
    const data = await getHotspotList();
    hotspotList.value = data;
  } catch (error) {
    console.error('获取热点列表失败:', error);
    message.error('获取热点列表失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 选择热点
const selectHotspot = async (hotspot: HotspotApi.HotspotItem) => {
  selectedHotspot.value = hotspot;
  await loadInitialContent(hotspot.word);
};

// 获取初始文案内容
const loadInitialContent = async (hotspotWord: string) => {
  try {
    contentLoading.value = true;
    const params: HotspotApi.GetInitialContentParams = {
      content: `请根据【${hotspotWord}】热点内容写1条长度500字以内的文案，不要附加话题，只生成纯文案。`,
      domainName: 'vapi.weijuyunke.com',
      ipStatus: 2,
      requestIp: '**************',
    };

    const content = await getInitialContent(params);
    textContent.value = content || '暂时无法获取文案内容，请手动输入或稍后重试。';
  } catch (error) {
    console.error('获取初始文案失败:', error);
    message.error('获取初始文案失败，请重试');
    textContent.value = '暂时无法获取文案内容，请手动输入或稍后重试。';
  } finally {
    contentLoading.value = false;
  }
};

// 一键仿写
const handleRewrite = async () => {
  if (!textContent.value.trim()) {
    message.warning('请先输入内容');
    return;
  }

  try {
    isRewriting.value = true;

    // 保存原始内容
    const originalContent = textContent.value;

    // 清空现有内容
    textContent.value = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    console.log('开始一键仿写，原始内容:', originalContent);

    await streamHandler.request({
      url: '/mobile/ai_dialogue/spread',
      method: 'POST',
      body: {
        content: originalContent,
        type: 3, // 文案改写类型
      },
      onData: (data: string) => {
        console.log('收到流式数据:', data);

        // 处理不同格式的数据
        if (data === '\n') {
          // 保留换行符
          textContent.value += '\n';
        } else if (data.trim()) {
          try {
            // 尝试解析JSON数据
            const jsonData = JSON.parse(data);
            console.log('解析的JSON数据:', jsonData);
            if (jsonData && jsonData.data) {
              textContent.value += jsonData.data;
            } else {
              textContent.value += data;
            }
          } catch (parseError) {
            console.log('JSON解析失败，直接添加数据:', data);
            // 如果不是JSON格式，则直接添加到字段中
            textContent.value += data;
          }
        }
      },
      onComplete: () => {
        console.log('流式请求完成');
        message.success('仿写完成');
        isRewriting.value = false;
      },
      onError: (error: Error) => {
        console.error('仿写失败:', error);
        message.error(`仿写失败: ${error.message}`);
        isRewriting.value = false;
      },
    });
  } catch (error) {
    console.error('仿写失败:', error);
    message.error('仿写失败，请重试');
    isRewriting.value = false;
  }
};

// 去创作
const goToCreation = () => {
  if (!canGoToCreation.value) {
    return;
  }
  
  // 跳转到视频创作页面，并传递内容
  router.push({
    path: '/public-domain/video-creation',
    query: {
      content: textContent.value,
    },
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadHotspotList();
});
</script>

<template>
  <Page title="AI热点跟拍" description="基于热点内容快速生成创作文案" auto-content-height>
    <div class="hotspot-tracking-container">
      <div class="hotspot-layout">
        <!-- 左侧：热点列表区域 -->
        <div class="hotspot-list-section">
          <Card title="热点排行榜" class="hotspot-list-card">
            <template #extra>
              <Button type="text" size="small" @click="loadHotspotList">
                刷新
              </Button>
            </template>
            <Spin :spinning="loading">
              <div v-if="hotspotList.length === 0 && !loading" class="empty-state">
                <Empty description="暂无热点数据">
                  <template #image>
                    <div style="font-size: 48px;">🔥</div>
                  </template>
                  <Button type="primary" @click="loadHotspotList">重新加载</Button>
                </Empty>
              </div>
              <List v-else :data-source="hotspotList" class="hotspot-list">
                <template #renderItem="{ item, index }">
                  <List.Item
                    :class="[
                      'hotspot-item',
                      { 'hotspot-item-selected': selectedHotspot?.word === item.word }
                    ]"
                    @click="selectHotspot(item)"
                  >
                    <div class="hotspot-content">
                      <div class="hotspot-rank">{{ index + 1 }}</div>
                      <div class="hotspot-info">
                        <div class="hotspot-title">{{ item.word }}</div>
                        <div class="hotspot-heat">热度: {{ item.hotindex }}</div>
                      </div>
                      <Button type="primary" size="small" class="create-btn">
                        点击创作
                      </Button>
                    </div>
                  </List.Item>
                </template>
              </List>
            </Spin>
          </Card>
        </div>

        <!-- 右侧：创作区域 -->
        <div class="creation-section">
          <Card title="内容创作" class="creation-card">
            <div v-if="!selectedHotspot" class="no-selection">
              <Empty description="请先选择一个热点">
                <template #image>
                  <div style="font-size: 48px;">👈</div>
                </template>
                <div class="selection-tips">
                  <p>选择左侧热点后，系统将自动生成相关文案</p>
                  <p>您可以编辑文案内容，或使用"一键仿写"功能优化文案</p>
                </div>
              </Empty>
            </div>
            <div v-else class="creation-content">
              <!-- 热点标题显示 -->
              <div class="selected-hotspot">
                <h3>当前热点：{{ selectedHotspot.word }}</h3>
              </div>

              <!-- 内容输入框 -->
              <div class="content-input-section">
                <Spin :spinning="contentLoading">
                  <Input.TextArea
                    v-model:value="textContent"
                    placeholder="文案内容将自动生成..."
                    :rows="12"
                    class="content-textarea"
                  />
                </Spin>
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <Button
                  type="default"
                  :loading="isRewriting"
                  :disabled="!textContent.trim() || contentLoading"
                  @click="handleRewrite"
                  class="rewrite-btn"
                >
                  {{ isRewriting ? '正在仿写...' : '一键仿写' }}
                </Button>
                <Button
                  type="primary"
                  :disabled="!canGoToCreation || contentLoading"
                  @click="goToCreation"
                  class="go-creation-btn"
                >
                  去创作
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped>
.hotspot-tracking-container {
  padding: 16px;
}

.hotspot-layout {
  display: flex;
  gap: 16px;
  height: calc(100vh - 200px);
}

.hotspot-list-section {
  flex: 0 0 400px;
}

.creation-section {
  flex: 1;
}

.hotspot-list-card,
.creation-card {
  height: 100%;
}

.hotspot-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.hotspot-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 12px;
  border: 1px solid #f0f0f0;
}

.hotspot-item:hover {
  background-color: #f5f5f5;
  border-color: #1890ff;
}

.hotspot-item-selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.hotspot-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.hotspot-rank {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
  min-width: 30px;
  text-align: center;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hotspot-info {
  flex: 1;
}

.hotspot-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.hotspot-heat {
  font-size: 12px;
  color: #666;
}

.create-btn {
  flex-shrink: 0;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.selection-tips {
  margin-top: 16px;
  color: #666;
  line-height: 1.6;
}

.selection-tips p {
  margin: 4px 0;
}

.creation-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.selected-hotspot {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
}

.selected-hotspot h3 {
  margin: 0;
  color: #52c41a;
}

.content-input-section {
  flex: 1;
  margin-bottom: 16px;
}

.content-textarea {
  height: 100% !important;
  min-height: 300px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hotspot-layout {
    flex-direction: column;
    height: auto;
  }

  .hotspot-list-section {
    flex: none;
    margin-bottom: 16px;
  }

  .hotspot-list-card {
    height: 400px;
  }

  .creation-section {
    flex: none;
  }
}

@media (max-width: 768px) {
  .hotspot-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons .ant-btn {
    width: 100%;
  }
}
</style>
